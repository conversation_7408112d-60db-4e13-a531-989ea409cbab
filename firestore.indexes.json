{"indexes": [{"collectionGroup": "diagnostic_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "completed", "order": "ASCENDING"}, {"fieldPath": "nextActionDate", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "archives", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "year", "order": "DESCENDING"}]}, {"collectionGroup": "diagnostic_records", "queryScope": "COLLECTION", "fields": [{"fieldPath": "plantId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}