{"indexes": [{"collectionGroup": "diagnostic_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "completed", "order": "ASCENDING"}, {"fieldPath": "nextActionDate", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "archives", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "year", "order": "DESCENDING"}]}, {"collectionGroup": "diagnostics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}